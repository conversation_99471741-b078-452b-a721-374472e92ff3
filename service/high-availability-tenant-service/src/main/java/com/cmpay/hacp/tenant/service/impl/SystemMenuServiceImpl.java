package com.cmpay.hacp.tenant.service.impl;

import com.cmpay.hacp.bo.menu.MenuBO;
import com.cmpay.hacp.tenant.bo.TenantBO;
import com.cmpay.hacp.tenant.bo.TenantWorkspaceBO;
import com.cmpay.hacp.system.bo.menu.MenuActionMetaBO;
import com.cmpay.hacp.system.bo.menu.MenuTreeMetaBO;
import com.cmpay.hacp.system.bo.menu.PermMenuTreeMetaBO;
import com.cmpay.hacp.enums.MenuEnum;
import com.cmpay.hacp.tenant.service.TenantService;
import com.cmpay.hacp.tenant.service.TenantWorkspaceRoleService;
import com.cmpay.hacp.tenant.service.WorkspaceService;
import com.cmpay.hacp.system.service.adapter.SystemMenuServiceAdapter;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.cmpay.lemon.framework.utils.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.cmpay.hacp.constant.TenantConstant.WORKSPACE_ID;

@Service
@Primary
public class SystemMenuServiceImpl extends SystemMenuServiceAdapter {

    private static final Logger logger = LoggerFactory.getLogger(SystemMenuServiceImpl.class);

    @Resource
    private TenantWorkspaceRoleService tenantWorkspaceRoleService;

    @Resource
    private TenantService tenantService;

    @Resource
    private WorkspaceService workspaceService;


    @Override
    public PermMenuTreeMetaBO getUserMenus(String userId) {
        PermMenuTreeMetaBO menuTree = new PermMenuTreeMetaBO();
        //查询系统菜单
        List<MenuBO> menus;
        //查询项目菜单
        String workspaceId = WebUtils.getHttpServletRequest().getHeader(WORKSPACE_ID);
        if (JudgeUtils.isBlank(workspaceId)) {
            //默认取下拉列表第一个项目菜单，避免用户只在项目角色表中有菜单的情况
            List<TenantBO> tenants = tenantService.getOwnTenantList(userId);
            if (JudgeUtils.isNotEmpty(tenants)) {
                TenantWorkspaceBO tenantWorkspace = new TenantWorkspaceBO();
                tenantWorkspace.setTenantId(tenants.get(0).getTenantId());
                tenantWorkspace.setUserId(SecurityUtils.getLoginUserId());
                List<TenantWorkspaceBO> workspaces = workspaceService.getWorkspaceOwnList(tenantWorkspace);
                if (JudgeUtils.isNotEmpty(workspaces)) {
                    workspaceId = workspaces.get(0).getWorkspaceId();
                }
            }
        }
        if (JudgeUtils.isBlank(workspaceId)) {
            menus = menuExtDao.queryUserMenus(userId);
        } else {
            menus = tenantWorkspaceRoleService.getWorkspaceMenusByUserId(userId, workspaceId);
        }
        if (JudgeUtils.isEmpty(menus)) {
            return menuTree;
        }
        //菜单
        List<MenuTreeMetaBO> menuList = new ArrayList<>();
        //按钮
        List<MenuActionMetaBO> buttonList = new ArrayList<>();
        for (MenuBO menuDO : menus) {

            if (JudgeUtils.equalsIgnoreCase(menuDO.getType(), MenuEnum.BUTTON.getValue())) {
                //按钮
                buttonList.add(menuUtils.getMenuActionRspMetaDTO(menuDO));
            } else {
                //菜单
                buttonList.add(menuUtils.getMenuActionRspMetaDTO(menuDO));
                menuList.add(menuUtils.getMenuTreeRspMetaDTO(menuDO));
            }
        }
        if (JudgeUtils.isNotEmpty(menuList)) {
            //转为树形菜单
            menuTree.setMenuTreeList(menuUtils.getMenuTreeMetas(menuList));
        }
        menuTree.setMenuActionList(buttonList);
        return menuTree;
    }

}
