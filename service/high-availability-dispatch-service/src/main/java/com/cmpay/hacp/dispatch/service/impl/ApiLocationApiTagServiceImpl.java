package com.cmpay.hacp.dispatch.service.impl;

import com.cmpay.hacp.dispatch.bo.ApiLocationApiTagBO;
import com.cmpay.hacp.dispatch.dao.IApiLocationApiTagExtDao;
import com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDO;
import com.cmpay.hacp.dispatch.entity.ApiLocationApiTagDOKey;
import com.cmpay.hacp.dispatch.service.ApiLocationApiTagService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApiLocationApiTagServiceImpl implements ApiLocationApiTagService {

    @Autowired
    private IApiLocationApiTagExtDao apiLocationApiTagDao;

    @Override
    public void addApiLocationApiTag(ApiLocationApiTagBO apiLocationApiTagBO) {

        apiLocationApiTagDao.insert(apiLocationApiTagBO);
    }

    @Override
    public void deleteApiLocationApiTag(ApiLocationApiTagBO apiLocationApiTagBO) {
        ApiLocationApiTagDOKey apiLocationApiTagDOKey = new ApiLocationApiTagDOKey();
        apiLocationApiTagDOKey.setApiLocationId(apiLocationApiTagBO.getApiLocationId());
        apiLocationApiTagDOKey.setApiTagId(apiLocationApiTagBO.getApiTagId());
        apiLocationApiTagDao.delete(apiLocationApiTagDOKey);
    }

    @Override
    public void deleteByApiLocationId(Integer apiLocationId, String workspaceId) {
        ApiLocationApiTagDO apiLocationApiTagDO = new ApiLocationApiTagDO();
        apiLocationApiTagDO.setApiLocationId(apiLocationId);
        apiLocationApiTagDO.setWorkspaceId(workspaceId);
        apiLocationApiTagDao.deleteByApiLocationId(apiLocationApiTagDO);

    }

    @Override
    public List<ApiLocationApiTagBO> getApiLocationApiTagList(ApiLocationApiTagBO apiLocationApiTagBO) {
        return BeanConvertUtil.convertList(apiLocationApiTagDao.find(apiLocationApiTagBO), ApiLocationApiTagBO.class);
    }
}
