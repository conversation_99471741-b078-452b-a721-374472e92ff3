/*
 * @ClassName TenantWorkspaceMiddlewareDO
 * @Description
 * @version 1.0
 * @Date 2023-08-06 17:51:36
 */
package com.cmpay.hacp.tenant.entity;

import com.cmpay.hacp.system.entity.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class TenantWorkspaceMiddlewareDO extends BaseDO {
    /**
     * @Fields id 编号
     */
    private String id;
    /**
     * @Fields url 注册中心地址
     */
    private String url;
    /**
     * @Fields region 归属区域
     */
    private String region;
    /**
     * @Fields zone 归属机房
     */
    private String zone;
    /**
     * @Fields cluster 所属集群
     */
    private String cluster;
    /**
     * @Fields type 注册中心类型[eureka]
     */
    private String type;
    /**
     * @Fields configServerName 配置中心服务名
     */
    private String configServerName;
    /**
     * @Fields configUserName 配置中心用户名
     */
    private String configUserName;
    /**
     * @Fields configPassword 配置中心密码
     */
    private String configPassword;
    /**
     * @Fields profile 运行环境
     */
    private String profile;
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getConfigServerName() {
        return configServerName;
    }

    public void setConfigServerName(String configServerName) {
        this.configServerName = configServerName;
    }

    public String getConfigUserName() {
        return configUserName;
    }

    public void setConfigUserName(String configUserName) {
        this.configUserName = configUserName;
    }

    public String getConfigPassword() {
        return configPassword;
    }

    public void setConfigPassword(String configPassword) {
        this.configPassword = configPassword;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}