package com.cmpay.hacp.system.log.client;

import com.cmpay.hacp.api.BaseApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.dto.system.SystemLogDTO;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Validated
@FeignClient(name = "${hacp.management.discovery.name:high-availability-control-platform-app}", url = "${hacp.management.discovery.url:}", contextId = "systemStaticLogClient")
public interface SystemStaticLogClient {
    /**
     * 登记操作日志
     *
     * @param systemLogDto 操作日志详情
     * @return
     */
    @LogNoneRecord
    @PostMapping(value = VersionApi.VERSION_V1 + BaseApi.BASE_SYSTEM+"/log/add",consumes = "application/json")
    DefaultRspDTO<NoBody> addSystemLogInfo(@RequestBody SystemLogDTO systemLogDto);
}
