package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.executor.config.PrometheusProperties;
import com.cmpay.hacp.inspection.domain.model.prometheus.*;
import com.cmpay.hacp.inspection.infrastructure.executor.cache.PrometheusTokenCache;
import com.cmpay.lemon.common.exception.BusinessException;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PrometheusGatewayImpl单元测试
 * 使用MockWebServer模拟HTTP服务器
 */
@ExtendWith(MockitoExtension.class)
class PrometheusGatewayImplTest {

    @Mock
    private PrometheusTokenCache prometheusTokenCache;

    private PrometheusGatewayImpl prometheusGateway;
    private MockWebServer mockWebServer;
    private PrometheusProperties properties;

    @BeforeEach
    void setUp() throws Exception {
        // 启动MockWebServer
        mockWebServer = new MockWebServer();
        mockWebServer.start();
        
        // 创建真实的PrometheusProperties对象
        properties = new PrometheusProperties();
        properties.setBaseUrl(mockWebServer.url("/").toString().replaceAll("/$", ""));
        properties.setIndicatorUrl("/api/indicators");
        
        PrometheusProperties.Auth auth = new PrometheusProperties.Auth();
        auth.setUserId("testUser");
        auth.setUserKey("testKey");
        auth.setTokenUrl("/api/token");
        properties.setAuth(auth);
        
        PrometheusProperties.Http http = new PrometheusProperties.Http();
        http.setConnectTimeout(10000);
        http.setReadTimeout(30000);
        http.setWriteTimeout(30000);
        http.setMaxIdleConnections(10);
        http.setKeepAliveDuration(5);
        http.setEnableSslCheck(false);
        properties.setHttp(http);
        
        // 创建PrometheusGatewayImpl实例
        prometheusGateway = new PrometheusGatewayImpl(prometheusTokenCache, properties);
        
        // 初始化组件
        prometheusGateway.afterPropertiesSet();
    }

    @AfterEach
    void tearDown() throws IOException {
        if (mockWebServer != null) {
            mockWebServer.shutdown();
        }
        if (prometheusGateway != null) {
            prometheusGateway.destroy();
        }
    }

    @Test
    void testGetAuthToken_FromCache_Success() {
        // Given
        PrometheusToken cachedToken = PrometheusToken.builder()
                .accessToken("cached-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
        
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(cachedToken);

        // When
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("cached-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(prometheusTokenCache).getValidToken("testUser");
        // 验证没有发起HTTP请求
        assertEquals(0, mockWebServer.getRequestCount());
    }

    @Test
    void testGetAuthToken_FetchNew_Success() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        
        // Mock HTTP response
        String tokenResponseJson = """
            {
                "token": "new-access-token",
                "userId": "testUser",
                "expire": 3600,
                "msgCd": "FFM00000",
                "msgInfo": "Success"
            }
            """;
        
        mockWebServer.enqueue(new MockResponse()
                .setBody(tokenResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("new-access-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(prometheusTokenCache).getValidToken("testUser");
        verify(prometheusTokenCache).cacheToken(eq("testUser"), any(PrometheusToken.class));
        
        // 验证请求
        RecordedRequest request = mockWebServer.takeRequest();
        assertEquals("POST", request.getMethod());
        assertTrue(request.getPath().contains("/api/token"));
    }

    @Test
    void testGetAuthToken_HttpRequestFailed() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(500)
                .setBody("Internal Server Error"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> prometheusGateway.getAuthToken());
        
        assertEquals(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR.getMsgCd(), exception.getMsgCd());
        verify(prometheusTokenCache).getValidToken("testUser");
        verifyNoMoreInteractions(prometheusTokenCache);
    }

    @Test
    void testGetAuthToken_AuthenticationFailed() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        
        String errorResponseJson = """
            {
                "token": null,
                "userId": "testUser",
                "expire": 0,
                "msgCd": "FFM80001",
                "msgInfo": "Authentication failed"
            }
            """;
        
        mockWebServer.enqueue(new MockResponse()
                .setBody(errorResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> prometheusGateway.getAuthToken());
        
        assertEquals(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_UserStatusError() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        
        String errorResponseJson = """
            {
                "token": null,
                "userId": "testUser",
                "expire": 0,
                "msgCd": "FFM80002",
                "msgInfo": "User status error"
            }
            """;
        
        mockWebServer.enqueue(new MockResponse()
                .setBody(errorResponseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> prometheusGateway.getAuthToken());
        
        assertEquals(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_InvalidJsonResponse() throws Exception {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        
        mockWebServer.enqueue(new MockResponse()
                .setBody("invalid json")
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> prometheusGateway.getAuthToken());
        
        assertEquals(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_ConcurrentAccess() throws InterruptedException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser"))
                .thenReturn(null)  // 第一次调用返回null
                .thenReturn(PrometheusToken.builder()  // 后续调用返回缓存的token
                        .accessToken("concurrent-token")
                        .tokenType("Bearer")
                        .expiresAt(LocalDateTime.now().plusHours(1))
                        .build());

        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // When
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    PrometheusToken token = prometheusGateway.getAuthToken();
                    assertNotNull(token);
                } finally {
                    latch.countDown();
                }
            });
        }

        // Then
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        executor.shutdown();
        
        // 验证只有一个线程真正获取了新token，其他线程从缓存获取
        verify(prometheusTokenCache, atLeast(threadCount)).getValidToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_Success() throws Exception {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);

        String responseJson = """
            {
                "msgCd": "FFM00000",
                "msgInfo": "Success",
                "data": [],
                "total": 0,
                "pageNum": 1,
                "pageSize": 100
            }
            """;

        mockWebServer.enqueue(new MockResponse()
                .setBody(responseJson)
                .setResponseCode(200)
                .addHeader("Content-Type", "application/json"));

        // When
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result);
        assertEquals("FFM00000", result.getMsgCd());
        assertEquals("Success", result.getMsgInfo());
        assertTrue(result.isSuccess());
        
        // 验证请求
        RecordedRequest recordedRequest = mockWebServer.takeRequest();
        assertEquals("POST", recordedRequest.getMethod());
        assertTrue(recordedRequest.getPath().contains("/api/indicators"));
        assertTrue(recordedRequest.getHeader("Authorization").contains("Bearer valid-token"));
    }
}
