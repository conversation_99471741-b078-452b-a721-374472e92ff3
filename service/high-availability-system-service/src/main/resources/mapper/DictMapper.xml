<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.system.dao.IDictDao">

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.system.entity.DictDO">
        <id column="dict_id" property="dictId" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="label" property="label" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="DECIMAL"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        dict_id
        , value, label, type, description, sort, parent_id, create_user, create_time,
        update_user, update_time, remarks, status
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from sys_dict
        where dict_id = #{dictId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String">
        delete
        from sys_dict
        where dict_id = #{dictId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.system.entity.DictDO">
        insert into sys_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictId != null">
                dict_id,
            </if>
            <if test="value != null">
                value,
            </if>
            <if test="label != null">
                label,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictId != null">
                #{dictId,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="label != null">
                #{label,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=DECIMAL},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.system.entity.DictDO">
        update sys_dict
        <set>
            <if test="value != null">
                value = #{value,jdbcType=VARCHAR},
            </if>
            <if test="label != null">
                label = #{label,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=DECIMAL},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where dict_id = #{dictId,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.system.entity.DictDO">
        select
        <include refid="Base_Column_List"/>
        from sys_dict
        <where>
            <if test="dictId != null">
                and dict_id = #{dictId,jdbcType=VARCHAR}
            </if>
            <if test="value != null">
                and value = #{value,jdbcType=VARCHAR}
            </if>
            <if test="label != null">
                and label = #{label,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="description != null">
                and description = #{description,jdbcType=VARCHAR}
            </if>
            <if test="sort != null">
                and sort = #{sort,jdbcType=DECIMAL}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null">
                and remarks = #{remarks,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
