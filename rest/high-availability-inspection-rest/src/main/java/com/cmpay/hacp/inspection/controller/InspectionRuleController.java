package com.cmpay.hacp.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionRuleService;
import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.hacp.inspection.assembler.InspectionRuleDTOMapper;
import com.cmpay.hacp.inspection.assembler.TagDTOMapper;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.dto.*;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.framework.controller.BaseController;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 巡检规则接口
 */
@RestController
@Tag(name = "巡检规则管理")
@RequiredArgsConstructor
@RequestMapping(VersionApi.VERSION_V1 +"/inspection/rule")
public class InspectionRuleController extends BaseController {

    private final InspectionRuleService inspectionRuleService;

    private final InspectionRuleDTOMapper inspectionRuleDTOMapper;

    private final TagService tagService;
    private final TagDTOMapper tagDTOMapper;

    /**
     * 分页查询巡检规则列表
     *
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询巡检规则列表", description = "根据条件分页查询巡检规则列表")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionRuleController','inspection:rule:query')")
    public DefaultRspDTO<PageDTO<InspectionRuleRspDTO>> getRulePage(@Validated @RequestBody InspectionRuleQueryReqDTO reqDTO) {

        // 转换查询条件
        InspectionRule queryCondition = inspectionRuleDTOMapper.toInspectionRule(reqDTO);

        // 执行分页查询
        IPage<InspectionRule> page = inspectionRuleService.getRulePage(
                reqDTO.getPage(), queryCondition);

        // 转换结果
        List<InspectionRuleRspDTO> rspDTOList = inspectionRuleDTOMapper
                .toInspectionRuleRspDTOList(page.getRecords());

        // 批量查询并设置标签
        if (!page.getRecords().isEmpty()) {
            giveRspDTOTags(page.getRecords(), rspDTOList);
        }

        // 构建分页响应
        PageDTO<InspectionRuleRspDTO> result = new PageDTO<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal()
        );
        result.setRecords(rspDTOList);

        return DefaultRspDTO.newSuccessInstance(result);
    }

    private void giveRspDTOTags(List<InspectionRule> records, List<InspectionRuleRspDTO> rspDTOList) {
        // 1. 构建ruleId到tagIds的映射
        Map<String, List<Long>> ruleTagIdsMap = records.stream()
                .collect(Collectors.toMap(
                        InspectionRule::getRuleId,
                        rule -> rule.getTagIds() != null ? rule.getTagIds() : Collections.emptyList()
                ));

        // 2. 批量查询标签数据
        Map<String, List<TagDTO>> ruleTagsMap = new HashMap<>();
        ruleTagIdsMap.forEach((ruleId, tagIds) -> {
            ruleTagsMap.put(ruleId, inspectionRuleDTOMapper.toTagDtoList(tagService.getTagByTagIds(tagIds)));
        });

        // 3. 填充到响应DTO
        rspDTOList.forEach(dto -> {
            List<TagDTO> tags = ruleTagsMap.getOrDefault(dto.getRuleId(), Collections.emptyList());
            dto.setTags(tags);
        });
    }


    /**
     * 新增巡检规则
     */
    @PostMapping("/create")
    @Operation(summary = "创建巡检规则", description = "创建新的巡检规则")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionRuleController','inspection:rule:create')")
    public DefaultRspDTO<String> createRule(@Validated @RequestBody InspectionRuleReqDTO reqDTO) {
        InspectionRule inspectionRule = inspectionRuleDTOMapper.toInspectionRule(reqDTO);

        String ruleId = inspectionRuleService.createRule(inspectionRule);

        return DefaultRspDTO.newSuccessInstance(ruleId);
    }

    /**
     * 更新巡检规则
     *
     * @param reqDTO 请求参数
     * @return 更新结果
     */
    @PostMapping("/update")
    @Operation(summary = "更新巡检规则", description = "更新现有的巡检规则")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionRuleController','inspection:rule:update')")
    public DefaultRspDTO<NoBody> updateRule(@Validated @RequestBody InspectionRuleReqDTO reqDTO) {
        // 验证规则ID
        if (reqDTO.getRuleId() == null) {
            BusinessException.throwBusinessException(ErrorCodeEnum.RULE_ID_REQUIRED);
        }

        InspectionRule inspectionRule = inspectionRuleDTOMapper.toInspectionRule(reqDTO);

        // 调用服务更新规则
        boolean success = inspectionRuleService.updateRule(inspectionRule);

        if (!success) {
            BusinessException.throwBusinessException(ErrorCodeEnum.RULE_UPDATE_FAILED);
        }
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除巡检规则
     *
     * @param ruleId 规则ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{ruleId}")
    @Operation(summary = "删除巡检规则", description = "删除指定的巡检规则")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionRuleController','inspection:rule:delete')")
    public DefaultRspDTO<NoBody> deleteRule(
            @Parameter(name = "ruleId", description = "规则ID", required = true)
            @PathVariable("ruleId") String ruleId) {
        inspectionRuleService.deleteRule(ruleId);

        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 获取巡检规则详情
     *
     * @param ruleId 规则ID
     * @return 规则详情
     */
    @GetMapping("/detail/{ruleId}")
    @Operation(summary = "获取巡检规则详情", description = "获取指定巡检规则的详细信息")
    @ApiResponse(responseCode = "200", description = "成功")
    @PreAuthorize("hasPermission('InspectionRuleController','inspection:rule:query')")
    public DefaultRspDTO<InspectionRuleRspDTO> getRuleDetail(
            @Parameter(name = "ruleId", description = "规则ID", required = true)
            @PathVariable("ruleId") String ruleId) {

        // 获取规则详情
        InspectionRule inspectionRule = inspectionRuleService.getRuleDetail(ruleId);

        List<Long> tagIds = inspectionRule.getTagIds();
        List<com.cmpay.hacp.inspection.domain.model.common.Tag> tags = tagService.getTagByTagIds(tagIds);
        InspectionRuleRspDTO rspDTO = inspectionRuleDTOMapper.toInspectionRuleRspDTO(inspectionRule);
        rspDTO.setTags(tagDTOMapper.toTagDTOList(tags));

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }

}
