package com.cmpay.hacp.system.controller;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemRoleApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.bo.system.RoleBO;
import com.cmpay.hacp.dto.system.*;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.system.service.SystemRoleService;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.framework.annotation.QueryBody;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

/**
 * 系统角色服务
 *
 * <AUTHOR>
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping(VersionApi.VERSION_V1)
public class SystemRoleController {

    @Autowired
    private SystemRoleService systemRoleService;

    /**
     * 分页查询
     *
     * @param pageQueryReqDTO
     * @return
     */
    @ApiOperation("分页查询")
    @LogNoneRecord
    @GetMapping(SystemRoleApi.PAGE)
    @PreAuthorize("hasPermission('SystemRoleController','sys:role:query')")
    public GenericRspDTO<RolePageQueryRspDTO> page(@QueryBody RolePageQueryReqDTO pageQueryReqDTO) {
        RoleBO roleInfo = new RoleBO();
        BeanUtils.copyProperties(roleInfo, pageQueryReqDTO);
        PageInfo<RoleBO> rolesPage = systemRoleService.getRolesPage(pageQueryReqDTO.getPageSize(),
                pageQueryReqDTO.getPageNum(),
                SecurityUtils.getLoginUserId(),
                roleInfo);
        RolePageQueryRspDTO pageQueryRspDTO = new RolePageQueryRspDTO();
        BeanUtils.copyProperties(pageQueryRspDTO, rolesPage);
        pageQueryRspDTO.setRoles(rolesPage.getList());
        return GenericRspDTO.newSuccessInstance(pageQueryRspDTO);
    }

    /**
     * 查询应用的所有角色
     *
     * @return
     */
    @ApiOperation("查询用户所有角色")
    @LogNoneRecord
    @GetMapping(SystemRoleApi.GETALLROLES)
    public GenericRspDTO<RoleQueryDTO> getAllRoles() {
        RoleQueryDTO roleQueryDTO = new RoleQueryDTO();
        roleQueryDTO.setList(systemRoleService.getAllRoles(SecurityUtils.getLoginUserId()));
        return GenericRspDTO.newSuccessInstance(roleQueryDTO);
    }

    /**
     * 获取角色信息
     *
     * @param roleId
     * @return
     */
    @ApiOperation("获取角色信息")
    @LogRecord(title = "查询角色信息", action = "查询")
    @GetMapping(SystemRoleApi.GETROLEINFO)
    @PreAuthorize("hasPermission('SystemRoleController','sys:role:query')")
    public GenericRspDTO<RoleInfoRspDTO> getRoleInfo(@PathVariable("roleId") Long roleId) {
        RoleInfoRspDTO rspDTO = new RoleInfoRspDTO();
        rspDTO.setRoleInfo(systemRoleService.getRole(roleId));
        rspDTO.setMenuIds(systemRoleService.getMenuIdsByRoleId(roleId)
                .stream()
                .map(menuVO -> menuVO.getMenuId())
                .collect(Collectors.toList()));
        return GenericRspDTO.newSuccessInstance(rspDTO);
    }

    /**
     * 新增角色
     *
     * @param roleInfoReqDTO
     * @return
     */
    @ApiOperation("新增角色")
    @LogRecord(title = "新增角色信息", action = "新增")
    @PostMapping(SystemRoleApi.ADD)
    @PreAuthorize("hasPermission('SystemRoleController','sys:role:save')")
    public GenericRspDTO<NoBody> add(@RequestBody RoleInfoReqDTO roleInfoReqDTO) {
        String loginUserId = SecurityUtils.getLoginUserId();
        systemRoleService.add(loginUserId, roleInfoReqDTO.getRoleInfo(), roleInfoReqDTO.getMenuIdList());
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 修改角色
     *
     * @param roleInfoReqDTO
     * @return
     */
    @PostMapping(SystemRoleApi.UPDATE)
    @LogRecord(title = "修改角色信息", action = "修改")
    @PreAuthorize("hasPermission('SystemRoleController','sys:role:update')")
    public GenericRspDTO<NoBody> update(@RequestBody RoleInfoReqDTO roleInfoReqDTO) {
        String loginUserId = SecurityUtils.getLoginUserId();
        systemRoleService.update(loginUserId, roleInfoReqDTO.getRoleInfo(), roleInfoReqDTO.getMenuIdList());
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 角色删除
     *
     * @param deleteReqDTO
     * @return
     */
    @DeleteMapping(SystemRoleApi.DELETE)
    @LogRecord(title = "删除角色信息", action = "删除")
    @PreAuthorize("hasPermission('SystemRoleController','sys:role:delete')")
    public GenericRspDTO<NoBody> deleteBatch(@RequestBody RoleDeleteReqDTO deleteReqDTO) {
        systemRoleService.deleteBatch(SecurityUtils.getLoginUserId(), deleteReqDTO.getRoleIds());
        return GenericRspDTO.newSuccessInstance();
    }
}
