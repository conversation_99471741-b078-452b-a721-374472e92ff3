package com.cmpay.hacp.inspection.domain.executor.gateway;

import com.cmpay.hacp.inspection.domain.executor.model.ScriptExecutionResult;
import com.cmpay.hacp.inspection.domain.executor.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.executor.model.SshConnectionConfig;

public interface SSHGateway {
    ScriptExecutionResult executeScript(SshConnectionConfig connectionConfig,
                                        ScriptExecutionRequest request);

    ScriptExecutionResult executeCommand(SshConnectionConfig connectionConfig,
                                         String command);
}
