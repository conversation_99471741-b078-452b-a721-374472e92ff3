package com.cmpay.hacp.inspection.domain.model.prometheus;

import lombok.Data;

/**
 * Prometheus Token响应对象
 */
@Data
public class PrometheusTokenResponse {
    
    /**
     * 令牌
     */
    private String token;
    
    /**
     * 用户名
     */
    private String userId;
    
    /**
     * 到期时间（单位秒）
     */
    private Long expire;
    
    /**
     * 返回码
     */
    private String msgCd;
    
    /**
     * 返回信息
     */
    private String msgInfo;
    
    /**
     * 检查响应是否成功
     * 
     * @return true如果成功，否则false
     */
    public boolean isSuccess() {
        return "FFM00000".equals(msgCd);
    }
    
    /**
     * 检查是否为账户或密码错误
     * 
     * @return true如果是账户或密码错误，否则false
     */
    public boolean isAuthError() {
        return "FFM80001".equals(msgCd);
    }
    
    /**
     * 检查是否为用户状态异常
     * 
     * @return true如果是用户状态异常，否则false
     */
    public boolean isUserStatusError() {
        return "FFM80002".equals(msgCd);
    }
}
