package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.bo.system.*;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.StatusConstans;
import com.cmpay.hacp.system.bo.system.LoginBO;
import com.cmpay.hacp.system.bo.system.LoginHistoryLogBO;
import com.cmpay.hacp.system.bo.system.UserLoginBO;
import com.cmpay.hacp.system.service.*;
import com.cmpay.hacp.utils.network.IpUtil;
import com.cmpay.hacp.utils.crypto.PasswordUtil;
import com.cmpay.hacp.utils.crypto.SM2EncryptorUtil;
import com.cmpay.hacp.utils.crypto.SM4EncryptorUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import com.cmpay.lemon.framework.utils.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/05/13 9:31
 * @since 1.0.0
 */
@Service
public class SystemLoginServiceImpl implements SystemLoginService {

    @Autowired
    private SystemUserService systemUserService;

    @Autowired
    private SystemDepartmentService systemDepartmentService;

    @Autowired
    private SystemAccessTokenService systemAccessTokenService;

    @Autowired(required = false)
    private SystemCipherService systemCipherService;

    @Autowired(required = false)
    private SystemLoginHistoryLogService systemLoginHistoryLogService;

    @Autowired(required = false)
    private SystemLoginLatestInfoService systemLoginLatestInfoService;
    @Autowired
    private SystemCacheService systemCacheService;

    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public UserLoginBO login(UserLoginBO userLoginBO) {
        //验证登录密码
        LoginBO loginBO = new LoginBO();
        loginBO.setUserName(userLoginBO.getUsername());
        String password = null;
        try {
            String sm4Password = SM4EncryptorUtil.decryptEcb(systemCipherService.getSm4RandomSalt(userLoginBO.getUsername(), userLoginBO.getCaptchaReqId()),
                    userLoginBO.getPassword());
            //前端sm2公钥加密,后台sm2私钥解密。
            password = SM2EncryptorUtil.decrypt(systemCipherService.getSm2PrivateKey(applicationName),sm4Password);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgEnum.WRONG_USERNAME_OR_PASSWORD);
        } finally {
            systemCacheService.delete(CommonConstant.SM4_RANDOM_SALT+userLoginBO.getUsername()+userLoginBO.getCaptchaReqId());
        }
        loginBO.setPassword(systemUserService.encryptPassword(password));
        loginBO.setCaptchaReqId(userLoginBO.getCaptchaReqId());
        loginBO.setCaptchaCode(userLoginBO.getCaptchaCode());
        LoginBO loginRspDTO = this.passwordLogin(loginBO);
        return this.getUserLoginBO(loginRspDTO);
    }

    @Override
    public UserLoginBO getUserLoginBO(LoginBO loginRspDTO) {
        HttpServletRequest httpServletRequest = WebUtils.getHttpServletRequest();
        LoginHistoryLogBO historyLogBO = new LoginHistoryLogBO();
        SessionTokenVO sessionToken = loginRspDTO.getSessionTokenVO();
        String userId = sessionToken.getUpmsUserId();
        UserBO userInfo = systemUserService.getUserInfo(userId);
        if (JudgeUtils.isNull(userInfo)) {
            BusinessException.throwBusinessException(MsgEnum.USERNAME_NOT_EXISTS);
        }
        if (JudgeUtils.isBlank(userInfo.getHasRole()) ||
                !JudgeUtils.equalsIgnoreCase(userInfo.getHasRole(), StatusConstans.HAS_ROLE.getValue())) {
            BusinessException.throwBusinessException(MsgEnum.USER_NOT_HAS_ROLE);
        }
        //禁用用户不能登录
        if (JudgeUtils.equalsIgnoreCase(StatusConstans.DISABLE.getValue(), userInfo.getStatus())) {
            BusinessException.throwBusinessException(MsgEnum.LOGIN_STATUS_CHECK_EXCEPTION);
        }
        //个人基本信息
        UserLoginBO userResultLoginBO = new UserLoginBO();
        userResultLoginBO.setUserInfo(loginRspDTO.getSessionTokenVO());
        userResultLoginBO.setPwdNeedToModify(loginRspDTO.getPwdNeedToModify());
        //查询上一次登录日期
        LoginHistoryLogBO loginHistoryLog = systemLoginLatestInfoService.queryLoginLatestInfo(userId);
        userResultLoginBO.setLoginHistory(loginHistoryLog);
        //上次登录时间
        if (JudgeUtils.isNotBlank(loginRspDTO.getLastLoginTime())) {
            //首先取权限中心的
            userResultLoginBO.setLastLoginTime(loginRspDTO.getLastLoginTime());
        }
        if (JudgeUtils.isBlank(userResultLoginBO.getLastLoginTime())) {
            //其次取当前系统
            if (JudgeUtils.isNotNull(loginHistoryLog)) {
                userResultLoginBO.setLastLoginTime(DateTimeUtils.formatLocalDateTime(loginHistoryLog.getLoginTime()));
            } else {
                //取当前时间
                userResultLoginBO.setLastLoginTime(DateTimeUtils.getCurrentDateTimeStr());
            }
        }

        // 更新登录时间
        userInfo.setLastLoginTime(DateTimeUtils.parseLocalDateTime(userResultLoginBO.getLastLoginTime()));
        systemUserService.updateDateTime(userInfo);

        //登记登录历史
        historyLogBO.setUserId(userId);
        historyLogBO.setUserName(sessionToken.getUserName());
        historyLogBO.setName(sessionToken.getFullName());
        historyLogBO.setMobile(sessionToken.getMobile());
        historyLogBO.setLoginIp(IpUtil.getIpAddr(httpServletRequest));
        LocalDate localDate = DateTimeUtils.getCurrentLocalDate();
        LocalDateTime localDateTime = DateTimeUtils.getCurrentLocalDateTime();
        historyLogBO.setLoginTerminal(httpServletRequest.getHeader("user-agent"));
        historyLogBO.setLoginFrom(applicationName);
        historyLogBO.setLoginDate(localDate);
        historyLogBO.setLoginTime(localDateTime);
        historyLogBO.setRequestId(LemonUtils.getRequestId());
        systemLoginHistoryLogService.addLoginHistoryLog(historyLogBO);
        return userResultLoginBO;
    }

    @Override
    public LoginBO passwordLogin(LoginBO loginBO) {
        UserBO userVO = new UserBO();
        userVO.setUserName(loginBO.getUserName());
        UserBO userInfo = systemUserService.getUserInfo(userVO);
        if (JudgeUtils.isNull(userInfo)) {
            BusinessException.throwBusinessException(MsgEnum.WRONG_USERNAME_OR_PASSWORD);
        }
        if (JudgeUtils.isBlank(userInfo.getPassword())) {
            BusinessException.throwBusinessException(MsgEnum.WRONG_USERNAME_OR_PASSWORD);
        }
        //注意，此处 Sm2Utils.decrypt解密 与之前的 AbstractSystemLoginService.decryptData 并不多余，兼容之前权限中心密码加密规则，权限中心是权限中心本身做的解密。
        String decryptPassword = systemUserService.decryptPassword(loginBO.getPassword());
        //数据库存储的是加盐后 Sm2Utils.encrypt 算法 公钥加密的密码
        String decryptDataBasePassword = PasswordUtil.decryptPassword(userInfo.getPassword(), systemAccessTokenService.getAesKey());
        if (!JudgeUtils.equalsIgnoreCase(decryptPassword, decryptDataBasePassword)) {
            BusinessException.throwBusinessException(MsgEnum.WRONG_USERNAME_OR_PASSWORD);
        }
        return this.getLoginRspDTO(userInfo);
    }

    private LoginBO getLoginRspDTO(UserBO userInfo) {
        LoginBO loginBO = new LoginBO();
        SessionTokenVO sessionTokenVO = new SessionTokenVO();
        sessionTokenVO.setUserName(userInfo.getUserName());
        sessionTokenVO.setUpmsUserId(userInfo.getUserId());
        sessionTokenVO.setMobile(userInfo.getMobile());
        sessionTokenVO.setFullName(userInfo.getFullName());
        sessionTokenVO.setCstUserId(userInfo.getCstUserId());
        sessionTokenVO.setHasRole(userInfo.getHasRole());
        sessionTokenVO.setPwdModifyTime(userInfo.getPwdModifyTime());
        sessionTokenVO.setScimUserId(userInfo.getScimUserId());
        sessionTokenVO.setScimUserName(userInfo.getScimUserName());
        if (JudgeUtils.isNotEmpty(userInfo.getDeptId())) {
            sessionTokenVO.setDeptName(Optional.ofNullable(systemDepartmentService.getDepartmentInfo(userInfo.getUserId(),
                    userInfo.getDeptId())).map(DeptBO::getDeptName).orElse(null));
        }
        loginBO.setSessionTokenVO(sessionTokenVO);
        return loginBO;
    }
}
