package com.cmpay.hacp.tenant.client;

import com.cmpay.lemonframework.redis.ConditionalOnRedisContext;
import com.cmpay.lemonframework.redis.annotation.RedisClient;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 */
@Configuration
@RedisClient(prefix = "lemon.cache.redis",
        name = "redisCache",
        configuration = {RedisCacheClient.RedisCacheConfiguration.class},
        registerRedisTemplate = true,
        beanNames = {"cacheRedisTemplate"})
public class RedisCacheClient implements CommandLineRunner {

    private final static Logger logger = LoggerFactory.getLogger(RedisCacheClient.class);

    @Override
    public void run(String... args) {
        logger.info("load RedisStringCacheClient ...");
    }

    @ConditionalOnRedisContext
    static class RedisCacheConfiguration {

        @Bean
        public RedisTemplate cacheRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
            RedisTemplate template = new RedisTemplate();
            template.setConnectionFactory(redisConnectionFactory);
            ObjectMapper mapper = new ObjectMapper(); //jsr310,localeDate
            mapper.registerModule(new JavaTimeModule());
            mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
            mapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
            Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
            jackson2JsonRedisSerializer.setObjectMapper(mapper);

            RedisSerializer<String> stringSerializer = new StringRedisSerializer();

            template.setKeySerializer(stringSerializer);
            template.setHashKeySerializer(stringSerializer);
            template.setValueSerializer(jackson2JsonRedisSerializer);
            template.setHashValueSerializer(jackson2JsonRedisSerializer);

            template.afterPropertiesSet();
            return template;
        }
    }


}
