package com.cmpay.hacp.dashboard.service.impl;

import com.cmpay.hacp.dashboard.DashboardProperties;
import com.cmpay.hacp.dashboard.bo.PanelInfo;
import com.cmpay.hacp.dashboard.bo.PanelProvider;
import com.cmpay.hacp.dashboard.bo.PanelType;
import com.cmpay.hacp.dashboard.bo.PanelVariables;
import com.cmpay.hacp.dashboard.dao.IDashboardPanelsDao;
import com.cmpay.hacp.dashboard.entity.DashboardPanelsDO;
import com.cmpay.hacp.dashboard.entity.DashboardPanelsDOKey;
import com.cmpay.hacp.dashboard.grafana.GrafanaHelper;
import com.cmpay.hacp.dashboard.grafana.GrafanaPanel;
import com.cmpay.hacp.dashboard.service.PanelService;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.cmpay.hacp.dashboard.util.PanelDomainUtils.deserializeOptions;
import static com.cmpay.hacp.dashboard.util.PanelDomainUtils.serializeOptions;

@Slf4j
@Service
@Transactional
public class PanelServiceImpl implements PanelService {
    private final DashboardProperties properties;
    private final IDashboardPanelsDao panelsDao;
    private final GrafanaHelper grafanaHelper;
    private Map<String, Object> defaultDashboard;

    public PanelServiceImpl(DashboardProperties properties,
                            IDashboardPanelsDao panelsDao,
                            GrafanaHelper grafanaHelper) {
        this.properties = properties;
        this.panelsDao = panelsDao;
        this.grafanaHelper = grafanaHelper;
    }

    public void onApplicationReady(String workspaceId) {
        log.info("Application ready, create default dashboard");
        this.defaultDashboard = grafanaHelper.prepareDashboard(workspaceId);
        log.info("Detected default dashboard: {}", this.defaultDashboard);
    }

    private PanelInfo toPanelInfo(DashboardPanelsDO panelsDO) {
        PanelInfo info = new PanelInfo();
        info.setWorkspaceId(panelsDO.getWorkspaceId());
        info.setType(PanelType.valueOf(panelsDO.getType()));
        info.setProvider(PanelProvider.valueOf(panelsDO.getProvider()));
        info.setUrl(panelsDO.getUrl());
        info.setVariables(deserializeOptions(panelsDO.getVariables()));
        info.setExtraOptions(deserializeOptions(panelsDO.getExtraOptions()));
        info.setEmbedUrlHttp(toEmbedUrl(properties.getGrafana().getDashboardUrlHttp(),
                panelsDO.getUrl(), deserializeOptions(panelsDO.getExtraOptions())));
        info.setEmbedUrlHttps(toEmbedUrl(properties.getGrafana().getDashboardUrlHttps(),
                panelsDO.getUrl(), deserializeOptions(panelsDO.getExtraOptions())));
        info.setStatus(panelsDO.getStatus());
        info.setOperatorId(panelsDO.getOperatorId());
        info.setOperatorName(panelsDO.getOperatorName());
        info.setCreateTime(panelsDO.getCreateTime());
        info.setUpdateTime(panelsDO.getUpdateTime());
        return info;
    }

    private String toEmbedUrl(String dashboardUrl, String panelUrl, Map<String, Object> extraOptions) {
        if (StringUtils.isBlank(panelUrl)) {
            return "";
        }
        panelUrl = StringUtils.replaceOnce(panelUrl, "/d/", "/d-solo/");
        Map<String, String> options = new HashMap<>(properties.getGrafana().getDashboardOptions());
        if (extraOptions != null) {
            extraOptions.forEach((key, value) -> options.put(key, value.toString()));
        }
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(dashboardUrl)
                .path(panelUrl);
        options.forEach(builder::queryParam);
        return builder.build().toUriString();
    }

    private List<PanelInfo> toPanelInfoList(List<DashboardPanelsDO> panelsDOList) {
        return panelsDOList.stream().map(this::toPanelInfo).collect(Collectors.toList());
    }

    @Override
    public PanelInfo queryPanel(String workspaceId, PanelType type) {
        if (JudgeUtils.isBlank(workspaceId)) {
            BusinessException.throwBusinessException("HAC10002");
        }
        if (type == null) {
            BusinessException.throwBusinessException("HAC10003");
        }
        DashboardPanelsDOKey key = new DashboardPanelsDOKey();
        key.setWorkspaceId(workspaceId);
        key.setType(type.name());
        DashboardPanelsDO panelsDO = panelsDao.get(key);
        if (panelsDO == null) {
            BusinessException.throwBusinessException("HAC10001");
        }
        onApplicationReady(workspaceId);
        return toPanelInfo(panelsDO);
    }

    @Override
    public List<PanelInfo> listPanels(String workspaceId) {
        return listPanels(workspaceId, true);
    }

    @Override
    public List<PanelInfo> listPanels(String workspaceId, boolean autoCreate) {
        if (JudgeUtils.isBlank(workspaceId)) {
            BusinessException.throwBusinessException("HAC10002");
        }
        onApplicationReady(workspaceId);
        Set<String> types = Arrays.stream(PanelType.values()).map(PanelType::name).collect(Collectors.toSet());
        DashboardPanelsDO condition = new DashboardPanelsDO();
        condition.setWorkspaceId(workspaceId);
        List<DashboardPanelsDO> panelsDOList = panelsDao.find(condition);
        if (panelsDOList == null) {
            panelsDOList = new ArrayList<>(types.size());
        }
        if (JudgeUtils.isEmpty(panelsDOList) && !autoCreate) {
            BusinessException.throwBusinessException("HAC10001");
        }
        if (panelsDOList.size() < types.size() && autoCreate) {
            List<GrafanaPanel> grafanaPanels = grafanaHelper.dashboardPanelsByUID((String) defaultDashboard.get("uid"));
            if (grafanaPanels == null || grafanaPanels.isEmpty()) {
                BusinessException.throwBusinessException("HAC10011");
            }
            updateDashboards(workspaceId, grafanaPanels, panelsDOList);
            panelsDOList = panelsDao.find(condition);
        }
        return toPanelInfoList(panelsDOList);
    }

    private DashboardPanelsDO newPanelsDO(String workspaceId, GrafanaPanel panel) {
        DashboardPanelsDO panelsDO = new DashboardPanelsDO();
        panelsDO.setWorkspaceId(workspaceId);
        panelsDO.setType(panel.getType().name());
        panelsDO.setProvider(PanelProvider.GRAFANA.name());
        panelsDO.setUrl((String) defaultDashboard.get("url"));
        panelsDO.setVariables(serializeOptions(PanelVariables.get(panel.getType())));
        Map<String, Object> extraOptions = new HashMap<>();
        extraOptions.put("panelId", panel.getId());
        panelsDO.setExtraOptions(serializeOptions(extraOptions));
        panelsDO.setStatus(true);
        panelsDO.setOperatorId("SYSTEM");
        panelsDO.setOperatorName("SYSTEM");
        panelsDO.setCreateTime(LocalDateTime.now());
        panelsDO.setUpdateTime(panelsDO.getCreateTime());
        return panelsDO;
    }

    private void updateDashboards(String workspaceId, List<GrafanaPanel> panels,
                                  List<DashboardPanelsDO> panelsDOList) {
        for (DashboardPanelsDO panelsDO : panelsDOList) {
            DashboardPanelsDOKey key = new DashboardPanelsDOKey();
            key.setWorkspaceId(panelsDO.getWorkspaceId());
            key.setType(panelsDO.getType());
            log.debug("Delete panel: {}", key);
            panelsDao.delete(key);
        }
        for (GrafanaPanel panel : panels) {
            DashboardPanelsDO panelsDO = newPanelsDO(workspaceId, panel);
            log.debug("Insert panel: {}", panelsDO);
            panelsDao.insert(panelsDO);
        }
    }
}
