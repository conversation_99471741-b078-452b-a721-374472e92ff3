package com.cmpay.hacp.emergency.service.camunda.Impl;

import com.cmpay.hacp.emergency.service.camunda.ProcessTaskService;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.rest.dto.task.CommentDto;
import org.camunda.bpm.engine.rest.dto.task.TaskDto;
import org.camunda.bpm.engine.task.Comment;
import org.camunda.bpm.engine.task.Task;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/15 9:56
 */
@RequiredArgsConstructor
@Service
public class ProcessTaskServiceImpl implements ProcessTaskService {

    private final TaskService taskService;

    @Override
    public List<TaskDto> getCurrentTasksById(String businessKey) {
        List<Task> matchingTasks = taskService.createTaskQuery()
                .processInstanceBusinessKey(businessKey)
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .active().list();
        List<TaskDto> tasks = new ArrayList<>();
        for (Task task : matchingTasks) {
            TaskDto returnTask = TaskDto.fromEntity(task);
            tasks.add(returnTask);
        }
        return tasks;
    }

    @Override
    @Async
    public void completeTaskAsync(TaskDto task, String comment) {
        if (JudgeUtils.isNotBlank(comment)) {
            taskService.createComment(task.getId(), task.getProcessInstanceId(), comment);
        }
        taskService.complete(task.getId());
    }

    @Override
    public void completeTask(TaskDto task, String comment) {
        if (JudgeUtils.isNotBlank(comment)) {
            taskService.createComment(task.getId(), task.getProcessInstanceId(), comment);
        }
        taskService.complete(task.getId());
    }

    @Override
    public void setAssignee(String taskId, String userId) {
        taskService.setAssignee(taskId, userId);
    }

    @Override
    public void setOwner(String taskId, String userId) {
        taskService.setOwner(taskId, userId);
    }

    @Override
    public Object getTaskVariable(String taskId, String variableName) {
        return taskService.getVariable(taskId, variableName);
    }

    @Override
    public void setTaskVariable(String taskId, String variableName, String variableValue) {
        taskService.setVariable(taskId, variableName, variableValue);
    }

    @Override
    public List<TaskDto> queryWaitingTasks(String userId, int firstResult, int maxResults) {
        List<Task> matchingTasks = taskService.createTaskQuery()
                .taskAssignee(userId).orderByTaskCreateTime()
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .desc().listPage(firstResult, maxResults);
        List<TaskDto> tasks = new ArrayList<>();
        for (Task task : matchingTasks) {
            TaskDto returnTask = TaskDto.fromEntity(task);
            tasks.add(returnTask);
        }
        return tasks;
    }

    @Override
    public TaskDto getTaskInfo(String taskId) {
        Task task = taskService.createTaskQuery()
                .taskId(taskId)
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .singleResult();
        return TaskDto.fromEntity(task);
    }

    @Override
    public CommentDto getCommentDto(String taskId) {
        List<Comment> taskComments = taskService.getTaskComments(taskId);
        return taskComments.isEmpty() ? null : CommentDto.fromComment(taskComments.get(taskComments.size()-1));
    }

    @Override
    public List<TaskDto> getActiveTaskByExecutionId(String executionId) {
        List<Task> list = taskService.createTaskQuery()
                .tenantIdIn(TenantUtils.getWorkspaceId())
                .executionId(executionId).active().list();
        //通常来讲只有一个任务
        List<TaskDto> tasks = new ArrayList<>();
        for (Task task : list) {
            TaskDto returnTask = TaskDto.fromEntity(task);
            tasks.add(returnTask);
        }
        return tasks;
    }
}
