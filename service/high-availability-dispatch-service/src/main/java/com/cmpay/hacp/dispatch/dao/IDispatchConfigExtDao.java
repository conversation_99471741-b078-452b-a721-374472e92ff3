/*
 * @ClassName IDispatchConfigDao
 * @Description 
 * @version 1.0
 * @Date 2024-06-05 17:35:35
 */
package com.cmpay.hacp.dispatch.dao;

import com.cmpay.hacp.dispatch.bo.DispatchConfigBO;
import com.cmpay.hacp.dispatch.entity.DispatchConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IDispatchConfigExtDao extends IDispatchConfigDao {

    List<DispatchConfigBO> findApiLocationDispatches(DispatchConfigBO dispatchDO);

    List<DispatchConfigBO> likeFind(DispatchConfigDO configDO);

    int pushUpdate(DispatchConfigDO configDO);

    List<DispatchConfigBO> getRunningDispatchConfig(@Param("workspaceId")String workspaceId,@Param("versions") List<String> versions);
}