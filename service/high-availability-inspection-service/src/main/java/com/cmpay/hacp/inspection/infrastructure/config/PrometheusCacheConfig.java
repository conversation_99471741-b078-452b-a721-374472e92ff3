package com.cmpay.hacp.inspection.infrastructure.config;

import com.cmpay.lemonframework.autoconfigure.hazelcast.ConfigCustomizer;
import com.hazelcast.config.Config;
import com.hazelcast.config.MapConfig;
import org.springframework.stereotype.Component;

/**
 * Prometheus缓存配置
 * 配置Prometheus token的分布式缓存TTL和其他参数
 */
@Component
public class PrometheusCacheConfig implements ConfigCustomizer {

    /**
     * Prometheus token缓存名称
     */
    public static final String PROMETHEUS_TOKENS_CACHE = "prometheusTokens";

    @Override
    public void customize(Config config) {
        config.addMapConfig(prometheusTokensMapConfig());
    }

    /**
     * 配置Prometheus tokens缓存
     * TTL设置为60分钟，与token的默认过期时间一致
     * 
     * @return MapConfig 缓存配置
     */
    private MapConfig prometheusTokensMapConfig() {
        MapConfig mapConfig = new MapConfig();
        mapConfig.setName(PROMETHEUS_TOKENS_CACHE);
        // 设置TTL为60分钟（3600秒）
        mapConfig.setTimeToLiveSeconds(60 * 60);
        // 设置最大空闲时间为30分钟（1800秒）
        mapConfig.setMaxIdleSeconds(30 * 60);
        // 设置最大条目数为1000（支持多用户场景）
        mapConfig.getEvictionConfig().setSize(1000);
        return mapConfig;
    }
}
