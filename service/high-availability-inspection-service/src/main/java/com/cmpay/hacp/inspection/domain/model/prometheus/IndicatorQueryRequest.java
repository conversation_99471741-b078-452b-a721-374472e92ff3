package com.cmpay.hacp.inspection.domain.model.prometheus;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 指标查询请求对象
 */
@Data
@Builder
public class IndicatorQueryRequest {
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 指标类型：mid-中间件，host-主机，container-容器
     */
    private String indicatorType;
    
    /**
     * 指标英文名
     */
    private String indicatorName;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 页数
     */
    private Integer pageSize;
    
    /**
     * 机房名称列表
     */
    private List<String> zones;
    
    /**
     * 特殊字段1
     */
    private String specialNameOne;
    
    /**
     * 特殊字段2
     */
    private String specialNameTwo;
    
    /**
     * 特殊字段3
     */
    private String specialNameThree;
    
    /**
     * 特殊字段4
     */
    private String specialNameFour;
    
    /**
     * 特殊字段5
     */
    private String specialNameFive;
    
    /**
     * 特殊字段6
     */
    private String specialNameSix;
    
    /**
     * 特殊字段7
     */
    private String specialNameSeven;
    
    /**
     * 特殊字段8
     */
    private String specialNameEight;
    
    /**
     * 特殊字段9
     */
    private String specialNameNine;
    
    /**
     * 特殊字段10
     */
    private String specialNameTen;
    
    /**
     * 获取所有特殊字段的Map
     * 
     * @return 特殊字段Map，key为字段名，value为字段值
     */
    public Map<String, String> getSpecialFields() {
        Map<String, String> fields = new java.util.HashMap<>();
        if (specialNameOne != null) fields.put("specialNameOne", specialNameOne);
        if (specialNameTwo != null) fields.put("specialNameTwo", specialNameTwo);
        if (specialNameThree != null) fields.put("specialNameThree", specialNameThree);
        if (specialNameFour != null) fields.put("specialNameFour", specialNameFour);
        if (specialNameFive != null) fields.put("specialNameFive", specialNameFive);
        if (specialNameSix != null) fields.put("specialNameSix", specialNameSix);
        if (specialNameSeven != null) fields.put("specialNameSeven", specialNameSeven);
        if (specialNameEight != null) fields.put("specialNameEight", specialNameEight);
        if (specialNameNine != null) fields.put("specialNameNine", specialNameNine);
        if (specialNameTen != null) fields.put("specialNameTen", specialNameTen);
        return fields;
    }
}
