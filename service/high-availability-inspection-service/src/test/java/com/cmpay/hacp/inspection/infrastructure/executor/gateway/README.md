# PrometheusGateway 单元测试

本目录包含了 PrometheusGateway 接口两个方法的完整单元测试。

## 测试文件说明

### 1. PrometheusGatewayImplTest.java
这是主要的单元测试文件，使用 MockWebServer 来模拟 HTTP 服务器，测试完整的 HTTP 交互流程。

**测试覆盖的场景：**

#### getAuthToken() 方法测试：
- ✅ `testGetAuthToken_FromCache_Success()` - 成功从缓存获取有效token
- ✅ `testGetAuthToken_FetchNew_Success()` - 缓存中没有token，成功获取新token
- ✅ `testGetAuthToken_HttpRequestFailed()` - HTTP请求失败（500错误）
- ✅ `testGetAuthToken_AuthenticationFailed()` - 认证失败（用户名密码错误）
- ✅ `testGetAuthToken_UserStatusError()` - 用户状态异常
- ✅ `testGetAuthToken_InvalidJsonResponse()` - 响应JSON格式无效
- ✅ `testGetAuthToken_ConcurrentAccess()` - 并发场景下的锁机制测试

#### getIndicatorDataList() 方法测试：
- ✅ `testGetIndicatorDataList_Success()` - 成功查询指标数据
- ✅ `testGetIndicatorDataList_InvalidRequest()` - 请求参数验证失败
- ✅ `testGetIndicatorDataList_TokenExpired()` - 401错误（token过期）导致缓存清除
- ✅ `testGetIndicatorDataList_QueryError()` - HTTP请求失败（500错误）
- ✅ `testGetIndicatorDataList_WithCompleteRequest()` - 完整请求参数的测试
- ✅ `testGetIndicatorDataList_InvalidJsonResponse()` - 响应解析失败

### 2. PrometheusGatewaySimpleTest.java
这是简化版的单元测试文件，主要测试业务逻辑、缓存逻辑和参数验证，不涉及实际的HTTP请求。

**测试覆盖的场景：**
- ✅ 缓存token的获取逻辑
- ✅ 请求参数验证逻辑
- ✅ PrometheusToken 对象的各种方法
- ✅ PrometheusTokenResponse 对象的状态判断方法
- ✅ IndicatorQueryRequest 的特殊字段处理
- ✅ IndicatorQueryResponse 的成功状态判断
- ✅ 错误代码枚举的验证

## 依赖说明

测试需要以下依赖：
```gradle
testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'
```

这个依赖已经添加到 `build.gradle` 文件中。

## 运行测试

### 运行所有PrometheusGateway相关测试：
```bash
./gradlew :service:high-availability-inspection-service:test --tests "*PrometheusGateway*"
```

### 运行完整的HTTP测试：
```bash
./gradlew :service:high-availability-inspection-service:test --tests PrometheusGatewayImplTest
```

### 运行简化的业务逻辑测试：
```bash
./gradlew :service:high-availability-inspection-service:test --tests PrometheusGatewaySimpleTest
```

### 运行特定的测试方法：
```bash
./gradlew :service:high-availability-inspection-service:test --tests PrometheusGatewayImplTest.testGetAuthToken_FromCache_Success
```

## 测试设计原则

1. **隔离性**：每个测试方法都是独立的，不依赖其他测试的状态
2. **可重复性**：测试可以重复运行，结果一致
3. **完整性**：覆盖了正常流程、异常流程、边界条件
4. **真实性**：使用MockWebServer模拟真实的HTTP交互
5. **可维护性**：测试代码结构清晰，易于理解和维护

## Mock策略

### PrometheusGatewayImplTest
- 使用 `MockWebServer` 模拟HTTP服务器
- 使用 `@Mock` 注解模拟 `PrometheusTokenCache`
- 创建真实的 `PrometheusProperties` 对象
- 测试完整的HTTP请求/响应流程

### PrometheusGatewaySimpleTest
- 使用 `@Mock` 注解模拟所有外部依赖
- 专注于业务逻辑测试
- 不涉及实际的HTTP请求

## 测试数据

测试使用的示例数据：
- 用户ID: "testUser"
- 用户密码: "testKey"
- Token: "test-token", "cached-token", "new-access-token"
- 指标类型: "host"
- 指标名称: "cpu_usage"

## 注意事项

1. 测试中使用的时间相关逻辑（如token过期时间）使用相对时间，避免时间依赖
2. 并发测试使用 `CountDownLatch` 确保线程同步
3. HTTP测试使用随机端口避免端口冲突
4. 测试完成后正确清理资源（关闭MockWebServer）

## 扩展建议

如果需要添加更多测试场景，可以考虑：
1. 网络超时测试
2. 大数据量响应测试
3. 特殊字符处理测试
4. 更复杂的并发场景测试
5. 性能测试（响应时间、吞吐量）
