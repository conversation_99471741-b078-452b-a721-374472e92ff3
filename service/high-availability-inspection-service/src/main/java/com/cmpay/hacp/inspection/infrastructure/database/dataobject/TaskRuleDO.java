package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("inspection_task_schedule")
public class TaskRuleDO extends BaseDO {
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 规则ID
     */
    private String ruleId;
}
