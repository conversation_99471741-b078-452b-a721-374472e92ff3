package com.cmpay.hacp.inspection.infrastructure.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.schema.Column;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Component
public class InspectionTenantHandler implements TenantLineHandler {
    // 公共数据的租户ID标识
    private static final String PUBLIC_TENANT_ID = "0";

    /**
     * 自定义租户查询条件
     * 关键：支持查询公共数据和当前租户数据
     */
    @Override
    public Expression getTenantId() {
//        String currentTenantId = TenantUtils.getWorkspaceId();
//
//        // 构建条件：tenant_id IN ('当前租户ID', '0')
//        // 0 为公共数据
//        Expression tenantCondition = new StringValue(currentTenantId);
//        Expression publicCondition = new StringValue(PUBLIC_TENANT_ID);
//
//        return new InExpression(
//                        new Column(getTenantIdColumn()),
//                        new ExpressionList<>(Arrays.asList(tenantCondition, publicCondition))
//                );

        return new StringValue(TenantUtils.getWorkspaceId());
    }

    /**
     * 自定义租户字段名
     */
    @Override
    public String getTenantIdColumn() {
        return "workspace_id";
    }

   /**
    * 过滤不需要多租户的表
    */
   @Override
   public boolean ignoreTable(String tableName) {
       // 系统配置表等不需要租户隔离的表
       Set<String> ignoreTables = new HashSet<>();
       ignoreTables.add("sys_dict");
       return ignoreTables.contains(tableName);
   }
}
