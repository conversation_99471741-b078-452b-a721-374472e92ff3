package com.cmpay.hacp.inspection.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 规则插件参数配置DTO
 */
@Data
@Schema(description = "规则插件参数配置DTO")
public class RulePluginParamDTO {

    @Schema(description = "插件ID", required = true, example = "1")
    @NotBlank(message = "HAI10001")
    private String pluginId;

    @Schema(description = "插件参数ID）", required = true, example = "1")
    @NotNull(message = "HAI10005")
    private Long pluginParamId;

    @Schema(description = "插件参数值", required = true, example = "1")
    @NotBlank(message = "HAI10006")
    private String pluginParamValue;

    @Schema(description = "持续时间（秒）", required = true, example = "300")
    @NotNull(message = "HAI10007")
    private Integer duration;

}
