package com.cmpay.hacp.message.service.adapter.impl;

import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.message.dao.IMessageContentExtDao;
import com.cmpay.hacp.message.dao.IMessageContentUserExtDao;
import com.cmpay.hacp.message.entity.MessageContentDO;
import com.cmpay.hacp.message.enums.MessageTypeEnum;
import com.cmpay.hacp.message.properties.EmailServerProperties;
import com.cmpay.hacp.message.utils.EmailHelper;
import com.cmpay.hacp.message.service.adapter.MessageSendAdapter;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/09/14 9:32
 * @since 1.0.0
 */
@Service("emailMessageServiceImpl")
public class EmailMessageSendServiceImpl extends MessageSendAdapter {

    @Autowired
    private EmailServerProperties emailServerProperties;

    public EmailMessageSendServiceImpl(SystemUserService systemUserService,
            IMessageContentUserExtDao messageContentUserExtDao,
            IMessageContentExtDao messageContentExtDao,
            EmailServerProperties emailServerProperties) {
        super(systemUserService, messageContentUserExtDao, messageContentExtDao, MessageTypeEnum.EMAIL);
        this.emailServerProperties = emailServerProperties;
    }

    @Override
    protected void sendCustomizeMessage() throws Exception{
        List<UserBO> users = systemUserService.getUsersByUserId(message.getMessageUserIds());
        String[] tos = users.stream().map(UserBO::getEmail).filter(JudgeUtils::isNotBlank).toArray(String[]::new);
        String to = String.join(CommonConstant.SEMICOLON, tos);
        EmailHelper emailHelper = new EmailHelper(emailServerProperties.getHost(),
                emailServerProperties.getPort(),
                emailServerProperties.getUser(),
                emailServerProperties.getSecret(),
                emailServerProperties.getPersonal());
        emailHelper.setAddress(to, message.getMessageTitle());
        emailHelper.send(message.getMessageContent());
    }

    @Override
    public void saveBefore(MessageContentDO data) {
        data.setOperatorId("system");
        data.setOperatorName("system");
    }
}
