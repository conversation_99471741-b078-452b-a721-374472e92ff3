package com.cmpay.hacp.controller;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.extend.container.client.dto.*;
import com.cmpay.hacp.message.bo.InteriorMessageBO;
import com.cmpay.hacp.extend.container.client.KubesphereClient;
import com.cmpay.hacp.dto.HelloWorldReqDTO;
import com.cmpay.hacp.dto.HelloWorldRspDTO;
import com.cmpay.hacp.message.enums.MessageChildTypeEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.message.service.adapter.impl.InteriorMessageSendServiceImpl;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@RestController
@ConditionalOnProperty(value = "spring.profiles.active", havingValue = "dev")
@RequestMapping("/demo")
public class HelloWorldController {
    @Autowired
    private InteriorMessageSendServiceImpl messageSendService;
    @Autowired
    private KubesphereClient kafesphereClient;

    @ApiOperation(value="hello world", notes="hello world", produces="application/json")
    @ApiResponse(code = 200, message = "hello world")
    @GetMapping("/hello")
    @LogNoneRecord
    public GenericRspDTO<HelloWorldRspDTO> hello(HelloWorldReqDTO reqDTO) {
        HelloWorldRspDTO rspDTO = new HelloWorldRspDTO();
        rspDTO.setMessage("Hello World!");
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, rspDTO);
    }

    @GetMapping("/send")
    @LogNoneRecord
    public GenericRspDTO<NoBody> send() throws Exception {
        InteriorMessageBO message = new InteriorMessageBO();
        message.setMessageChildType(MessageChildTypeEnum.EMERGENCY_APPROVAL);
        message.setMessageTitle("测试");
        message.setMessageContent("测试");
        message.setOperationId("1");
        message.setOperationName("1");
        ArrayList<String> messageUserIds = new ArrayList<>();
        messageUserIds.add("09f1ca36f4354bde947be04f52d7362c");
        message.setMessageUserIds(messageUserIds);
        messageSendService.sendMessage(message);
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS);
    }

    @GetMapping("/getToken")
    @LogNoneRecord
    public GenericRspDTO<NoBody> getToken(){
        KubesphereTokenReqDTO reqDTO = new KubesphereTokenReqDTO();
        reqDTO.setClient_id("kubesphere");
        reqDTO.setGrant_type("password");
        reqDTO.setClient_secret("kubesphere");
        reqDTO.setUsername("lihuiquangy");
        reqDTO.setPassword("LIhuiquan@2024");
        KubesphereTokenRspDTO token = kafesphereClient.getToken(reqDTO);
        String token1 = "Bearer " + token.getAccessToken();
        KubesphereRspDTO zztestkf = kafesphereClient.getNs("zztestkf", token1);
        KubesphereClusterRspDTO zztestkf1 = kafesphereClient.getClusters("financial-middle", token1);
        KubesphereNsPodsDTO zztestkf2 = kafesphereClient.getNsPods("zztestkf", "awx",token1);
        KubesphereRspDTO zztestkf3 = kafesphereClient.getWorkspaces(token1);
        String zztestkf4 = kafesphereClient.deletePod("xxx","esda","",token1);
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS);
    }
}