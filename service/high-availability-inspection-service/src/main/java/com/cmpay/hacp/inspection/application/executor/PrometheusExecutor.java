package com.cmpay.hacp.inspection.application.executor;

import com.cmpay.hacp.inspection.domain.executor.model.ExecutionContext;
import com.cmpay.hacp.inspection.domain.executor.model.InspectionExecutor;
import com.cmpay.hacp.inspection.domain.executor.gateway.PrometheusGateway;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.IndicatorQueryResponse;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusQueryRequest;
import com.cmpay.hacp.inspection.domain.model.prometheus.PrometheusQueryResponse;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.result.model.InspectionResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Prometheus执行器
 * 使用PrometheusGateway执行Prometheus查询并返回巡检结果
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PrometheusExecutor implements InspectionExecutor {

    private final PrometheusGateway prometheusGateway;

    @Override
    public InspectionResult execute(InspectionTask task) {
        try {
            log.info("Executing Prometheus inspection for task: {}", task.getTaskId());

            IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                    .startTime(task.getStartTime())
                    .endTime(task.getEndTime())
                    .indicatorType("host")
                    .indicatorName("cpu_usage_rate")
                    .pageNum(1)
                    .pageSize(1000)
                    .build();

            // 执行Prometheus查询
            IndicatorQueryResponse response = prometheusGateway.getIndicatorDataList(request);

            // 处理查询结果
            if (response.isSuccess() && response.getData() != null) {
                log.debug("Prometheus query successful, result type: {}, result count: {}",
                         response.getData().getResultType(),
                         response.getData().getResult().size());

                // 这里可以根据具体的业务逻辑处理查询结果
                // 例如：检查指标值是否符合预期，生成巡检报告等

                return InspectionResult.builder()
                        .success(true)
                        .message("Prometheus查询执行成功")
                        .details("查询结果数量: " + response.getData().getResult().size())
                        .pluginName("PrometheusExecutor")
                        .scriptExecutionSuccess(true)
                        .ruleMatchingSuccess(true)
                        .build();
            } else {
                log.warn("Prometheus query failed or returned no data: {}", response.getError());
                return InspectionResult.builder()
                        .success(false)
                        .message("Prometheus查询失败: " + response.getError())
                        .pluginName("PrometheusExecutor")
                        .scriptExecutionSuccess(false)
                        .ruleMatchingSuccess(false)
                        .build();
            }

        } catch (Exception e) {
            log.error("Error executing Prometheus inspection for task: {}", task.getTaskId(), e);
            return InspectionResult.builder()
                    .success(false)
                    .message("Prometheus巡检执行异常: " + e.getMessage())
                    .pluginName("PrometheusExecutor")
                    .scriptExecutionSuccess(false)
                    .ruleMatchingSuccess(false)
                    .build();
        }
    }

    @Override
    public boolean supports(ExecutionContext context) {
        return context.getPluginType() == PluginType.SYSTEM_METRIC;
    }
}
