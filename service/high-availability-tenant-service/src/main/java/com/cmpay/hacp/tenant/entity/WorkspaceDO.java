/*
 * @ClassName WorkspaceDO
 * @Description
 * @version 1.0
 * @Date 2023-07-31 17:17:20
 */
package com.cmpay.hacp.tenant.entity;

import com.cmpay.hacp.system.entity.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class WorkspaceDO extends BaseDO {
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields workspaceName 项目名称
     */
    private String workspaceName;
    /**
     * 环境
     */
    private String profile;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getWorkspaceName() {
        return workspaceName;
    }

    public void setWorkspaceName(String workspaceName) {
        this.workspaceName = workspaceName;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }
}