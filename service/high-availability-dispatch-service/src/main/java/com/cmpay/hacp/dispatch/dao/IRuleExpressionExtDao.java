/*
 * @ClassName IRuleExpressionDao
 * @Description 
 * @version 1.0
 * @Date 2024-06-26 18:09:47
 */
package com.cmpay.hacp.dispatch.dao;

import com.cmpay.hacp.dispatch.entity.RuleExpressionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IRuleExpressionExtDao extends IRuleExpressionDao {

    List<RuleExpressionDO> likeFind(RuleExpressionDO ruleExpressionDO);

    int deleteByRuleId(Integer dispatchRuleId);
}