package com.cmpay.hacp.system.controller;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemDictApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.system.bo.system.DictBO;
import com.cmpay.hacp.dto.system.*;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.system.service.SystemDictionaryService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.annotation.QueryBody;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * 系统字典api
 *
 * <AUTHOR>
 */
@Api(tags = "字典管理")
@RestController
public class SystemDictionaryController {

    @Autowired
    private SystemDictionaryService systemDictionaryService;

    /**
     * 获取字典树形结构
     *
     * @return
     */
    @ApiOperation("字典树形结构")
    @LogNoneRecord
    @GetMapping(VersionApi.VERSION_V1 + SystemDictApi.TREE_DICT)
    public GenericRspDTO<DictListRspDTO> queryTreeDict() {
        List<DictBO> bos = systemDictionaryService.queryTreeDict();
        List<DictDTO> dtos = new ArrayList<>();
        if (JudgeUtils.isNotEmpty(bos)) {
            dtos = BeanConvertUtil.convertList(bos, DictDTO.class);
        }
        DictListRspDTO dictListRspDTO = new DictListRspDTO();
        dictListRspDTO.setDicts(dtos);
        return GenericRspDTO.newSuccessInstance(dictListRspDTO);
    }

    /**
     * 查询字典列表
     *
     * @param dictPageReqDTO
     * @return
     */
    @ApiOperation("查询分页列表")
    @LogNoneRecord
    @GetMapping(VersionApi.VERSION_V1 + SystemDictApi.PAGE)
    @PreAuthorize("hasPermission('SystemDictionaryController','sys:dict:query')")
    public GenericRspDTO<DictPageRspDTO> getPage(@QueryBody DictPageReqDTO dictPageReqDTO) {
        DictBO dictBO = new DictBO();
        BeanUtils.copyProperties(dictBO, dictPageReqDTO);
        PageInfo<DictBO> pageInfo = systemDictionaryService.getPage(dictPageReqDTO.getPageNum(), dictPageReqDTO.getPageSize(), dictBO);
        DictPageRspDTO dictPageRspDTO = new DictPageRspDTO();
        if (JudgeUtils.isNotEmpty(pageInfo.getList())) {
            List<DictDTO> dtos = BeanConvertUtil.convertList(pageInfo.getList(), DictDTO.class);
            dictPageRspDTO.setDicts(dtos);
        }
        BeanUtils.copyProperties(dictPageRspDTO, pageInfo);
        return GenericRspDTO.newSuccessInstance(dictPageRspDTO);
    }

    /**
     * 查询一组字典项的值
     *
     * @param dictListReqDTO
     * @return
     */
    @ApiOperation("查询一组字典项的值")
    @LogNoneRecord
    @GetMapping(VersionApi.VERSION_V1 + SystemDictApi.SELECT)
    public GenericRspDTO<DictListRspDTO> select(@QueryBody DictListReqDTO dictListReqDTO) {
        DictBO dictBO = new DictBO();
        BeanUtils.copyProperties(dictBO, dictListReqDTO);
        List<DictBO> bos = systemDictionaryService.queryDictInfos(dictBO);
        List<DictDTO> dtos = BeanConvertUtil.convertList(bos, DictDTO.class);
        DictListRspDTO dictListRspDTO = new DictListRspDTO();
        dictListRspDTO.setDicts(dtos);
        return GenericRspDTO.newSuccessInstance(dictListRspDTO);
    }


    /**
     * 查询一组字典项的值
     *
     * @return
     */
    @ApiOperation("查询一组非父项字典项的值")
    @LogNoneRecord
    @GetMapping(VersionApi.VERSION_V1 + SystemDictApi.SELECT_CHILDREN)
    public GenericRspDTO<DictListRspDTO> selectChildren(String type) {
        DictBO dictBO = new DictBO();
        dictBO.setType(type);
        List<DictBO> bos = systemDictionaryService.queryDictChildren(dictBO);
        List<DictDTO> dtos = BeanConvertUtil.convertList(bos, DictDTO.class);
        DictListRspDTO dictListRspDTO = new DictListRspDTO();
        dictListRspDTO.setDicts(dtos);
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, dictListRspDTO);
    }

    /**
     * 查询单个字典详情
     *
     * @param id
     * @return
     */
    @ApiOperation("查询详情")
    @LogRecord(title = "查询字典详情", action = "查询")
    @GetMapping(VersionApi.VERSION_V1 + SystemDictApi.INFO)
    @PreAuthorize("hasPermission('SystemDictionaryController','sys:dict:query')")
    public GenericRspDTO<DictRspDTO> info(@PathVariable("id") String id) {
        DictBO dictBO = systemDictionaryService.info(id);
        DictRspDTO dictRspDTO = new DictRspDTO();
        DictDTO dictDTO = new DictDTO();
        BeanUtils.copyProperties(dictDTO, dictBO);
        dictRspDTO.setDictInfo(dictDTO);
        return GenericRspDTO.newSuccessInstance(dictRspDTO);
    }

    /**
     * 新增字典
     *
     * @param dictReqDTO
     * @return
     */
    @PreAuthorize("hasPermission('SystemDictionaryController','sys:dict:save')")
    @PostMapping(VersionApi.VERSION_V1 + SystemDictApi.ADD)
    @LogRecord(title = "新增字典详情", action = "新增")
    public GenericRspDTO<NoBody> add(@RequestBody DictReqDTO dictReqDTO) {
        String loginUserId = SecurityUtils.getLoginUserId();
        DictDTO dictInfo = dictReqDTO.getDictInfo();
        DictBO dictBO = new DictBO();
        BeanUtils.copyProperties(dictBO, dictInfo);
        dictBO.setCreateUser(loginUserId);
        dictBO.setUpdateUser(loginUserId);
        systemDictionaryService.add(dictBO);
        return GenericRspDTO.newSuccessInstance();
    }


    /**
     * 修改字典
     *
     * @param dictReqDTO
     * @return
     */
    @PreAuthorize("hasPermission('SystemDictionaryController','sys:dict:update')")
    @PostMapping(VersionApi.VERSION_V1 + SystemDictApi.UPDATE)
    @LogRecord(title = "修改字典详情", action = "修改")
    public GenericRspDTO<NoBody> update(@RequestBody DictReqDTO dictReqDTO) {
        String loginUserId = SecurityUtils.getLoginUserId();
        DictDTO dictInfo = dictReqDTO.getDictInfo();
        DictBO dictBO = new DictBO();
        BeanUtils.copyProperties(dictBO, dictInfo);
        dictBO.setCreateUser(loginUserId);
        dictBO.setUpdateUser(loginUserId);
        systemDictionaryService.update(dictBO);
        return GenericRspDTO.newSuccessInstance();
    }


    /**
     * 删除字典集合
     *
     * @param dictDeleteReqDTO
     * @return
     */
    @PreAuthorize("hasPermission('SystemDictionaryController','sys:dict:delete')")
    @DeleteMapping(VersionApi.VERSION_V1 + SystemDictApi.DELETE)
    @LogRecord(title = "删除字典详情", action = "删除")
    public GenericRspDTO<NoBody> delete(@RequestBody DictDeleteReqDTO dictDeleteReqDTO) {
        systemDictionaryService.deleteDictInfo(dictDeleteReqDTO.getIds());
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 查询是否是管理员
     *
     * @param userId
     * @param applicationName
     * @return
     */
    @GetMapping(VersionApi.VERSION_V1 + SystemDictApi.IS_ADMIN)
    @LogNoneRecord
    public GenericRspDTO<Boolean> isAdmin(String userId, String applicationName) {
        boolean admin = systemDictionaryService.isAdmin(userId, applicationName);
        return GenericRspDTO.newSuccessInstance(admin);
    }
}
