package com.cmpay.hacp.system.service.impl;

import com.cmpay.hacp.system.bo.system.DictBO;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.SystemCipherService;
import com.cmpay.hacp.system.service.SystemDictionaryService;
import com.cmpay.hacp.system.service.SystemUserService;
import com.cmpay.hacp.system.service.adapter.SystemCipherServiceAdapter;
import com.cmpay.hacp.utils.crypto.ByteUtil;
import com.cmpay.hacp.utils.crypto.RsaEncryptorUtil;
import com.cmpay.hacp.utils.crypto.SM2EncryptorUtil;
import com.cmpay.hacp.utils.crypto.SM4EncryptorUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemonframework.smx.Sm4Utils;
import org.bouncycastle.pqc.legacy.math.linearalgebra.ByteUtils;
import org.bouncycastle.util.encoders.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.cmpay.hacp.constant.CommonConstant.SM4_RANDOM_SALT;

/**
 * <AUTHOR>
 */
@Service
public class SystemCipherServiceImpl extends SystemCipherServiceAdapter {

    private final static String SM2 = "SM2";
    private final static String SM4 = "SM4";
    private final static String RSA = "RSA";
    private final static String KEY = "key";
    private final static String PUBLIC_KEY = "publicKey";
    private final static String PRIVATE_KEY = "privateKey";
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Value("${spring.application.name}")
    private String applicationName;

    @Autowired
    private SystemDictionaryService systemDictionaryService;

    @Autowired
    private SystemCacheService systemCacheService;

    @Autowired
    private SystemUserService systemUserService;
    /**
     * 自注入，使用缓存
     */
    @Autowired
    @Lazy
    private SystemCipherService systemCipherService;

    @Override
    public String getSm2PublicKey(String applicationName) {
        DictBO dictBO = new DictBO();
        dictBO.setType(SM2);
        dictBO.setLabel(PUBLIC_KEY);
        List<DictBO> dictBOS = systemDictionaryService.queryDictInfos(dictBO);
        if (JudgeUtils.isEmpty(dictBOS)) {
            return null;
        }
        return dictBOS.get(0).getValue();
    }

    @Override
    public String getSm2PrivateKey(String applicationName) {
        DictBO dictBO = new DictBO();
        dictBO.setType(SM2);
        dictBO.setLabel(PRIVATE_KEY);
        List<DictBO> dictBOS = systemDictionaryService.queryDictInfos(dictBO);
        if (JudgeUtils.isEmpty(dictBOS)) {
            return null;
        }
        return dictBOS.get(0).getValue();
    }

    @Override
    public String getSm4Key(String applicationName) {
        DictBO dictBO = new DictBO();
        dictBO.setType(SM4);
        dictBO.setLabel(KEY);
        List<DictBO> dictBOS = systemDictionaryService.queryDictInfos(dictBO);
        if (JudgeUtils.isEmpty(dictBOS)) {
            return null;
        }
        return dictBOS.get(0).getValue();
    }

    @Override
    public String getRsaPublicKey(String applicationName) {
        DictBO dictBO = new DictBO();
        dictBO.setType(RSA);
        dictBO.setLabel(PUBLIC_KEY);
        List<DictBO> dictBOS = systemDictionaryService.queryDictInfos(dictBO);
        if (JudgeUtils.isEmpty(dictBOS)) {
            return null;
        }
        return dictBOS.get(0).getValue();
    }

    @Override
    public String getRsaPrivateKey(String applicationName) {
        DictBO dictBO = new DictBO();
        dictBO.setType(RSA);
        dictBO.setLabel(PRIVATE_KEY);
        List<DictBO> dictBOS = systemDictionaryService.queryDictInfos(dictBO);
        if (JudgeUtils.isEmpty(dictBOS)) {
            return null;
        }
        return new String(Base64.decode(dictBOS.get(0).getValue()), Charset.defaultCharset());
    }

    @Override
    public String sm2Decrypt(String encryptData) {
        String privateKey = systemCipherService.getSm2PrivateKey(applicationName);
        String decryptData = null;
        try {
            decryptData = SM2EncryptorUtil.decrypt(privateKey, encryptData);
        } catch (Exception e) {
            logger.error("sm2Decrypt err {}", e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.PARAM_PARSE_ERROR);
        }
        return decryptData;
    }

    @Override
    public String sm4Decrypt(String encryptData) {
        String privateKey = systemCipherService.getSm4Key(applicationName);
        byte[] privateKeys = ByteUtils.fromHexString(privateKey);
        byte[] encryptDatas = ByteUtils.fromHexString(encryptData);
        String decryptData = null;
        try {
            decryptData = new String(SM4EncryptorUtil.decryptEcbPadding(privateKeys, encryptDatas), Charset.defaultCharset());
        } catch (Exception e) {
            logger.error("sm4Decrypt err {}", e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.PARAM_PARSE_ERROR);
        }
        return decryptData;
    }


    @Override
    public String rsaDecrypt(String encryptData) {
        String privateKey = systemCipherService.getRsaPrivateKey(applicationName);
        String decryptData = RsaEncryptorUtil.decryptByPrivateKey(encryptData, privateKey);
        if (JudgeUtils.isBlank(decryptData)) {
            BusinessException.throwBusinessException(MsgEnum.PARAM_PARSE_ERROR);
        }
        return decryptData;
    }

    @Override
    public String setSm4RandomSalt(String username, String captchaReqId) {
        String characterAndNumber = "";
        try {
            String key = SM4_RANDOM_SALT + username + captchaReqId;
            characterAndNumber = ByteUtil.byteToHex(Sm4Utils.generateKey());
            systemCacheService.setValue(key, characterAndNumber, 60, TimeUnit.SECONDS);
        } catch (NoSuchAlgorithmException | NoSuchProviderException e) {
            logger.error("sm4 random salt error :{} ", e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.SM4_RANDOM_SALT_GENERATION_ERROR);
        }
        return characterAndNumber;
    }

    @Override
    public String getSm4RandomSalt(String username, String captchaReqId) {
        String key = SM4_RANDOM_SALT + username + captchaReqId;
        String characterAndNumber = (String) systemCacheService.getValue(key);
        if (JudgeUtils.isBlank(characterAndNumber)) {
            BusinessException.throwBusinessException(MsgEnum.SM4_RANDOM_SALT_CACHE_NULL_ERROR);
        }
        return characterAndNumber;
    }

    @Override
    public String otherModuleEncryptAndDecryptData(String key, String data) {
        if (JudgeUtils.isBlank(key)) {
            BusinessException.throwBusinessException(MsgEnum.SM4_AND_SM2_CACHE_KEY_NOT_NULL);
        }
        try {
            String sm4RandomSalt = getSm4RandomSalt(key, key);
            String sm4Password = SM4EncryptorUtil.decryptEcb(sm4RandomSalt, data);
            //前端sm2公钥加密,后台sm2私钥解密。
            data = SM2EncryptorUtil.decrypt(systemCipherService.getSm2PrivateKey(applicationName), sm4Password);
        } catch (Exception e) {
            logger.error(e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.SM4_AND_SM2_DECRYPTION_FAILED);
        }
        systemCacheService.delete(CommonConstant.SM4_RANDOM_SALT + key + key);
        return systemUserService.encryptPassword(data);
    }

    @Override
    public String decryptData(String key, String data) {
        if (JudgeUtils.isBlank(key)) {
            BusinessException.throwBusinessException(MsgEnum.SM4_AND_SM2_CACHE_KEY_NOT_NULL);
        }
        try {
            String sm4RandomSalt = getSm4RandomSalt(key, key);
            String sm4Password = SM4EncryptorUtil.decryptEcb(sm4RandomSalt, data);
            //前端sm2公钥加密,后台sm2私钥解密。
            data = SM2EncryptorUtil.decrypt(systemCipherService.getSm2PrivateKey(applicationName), sm4Password);
        } catch (Exception e) {
            logger.error(e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.SM4_AND_SM2_DECRYPTION_FAILED);
        }
        systemCacheService.delete(CommonConstant.SM4_RANDOM_SALT + key + key);
        return data;
    }

    @Override
    public String otherModuleEncryptAndDecryptData(String data) {
        return systemUserService.encryptPassword(data);
    }

    @Override
    public String otherModuleDecryptData(String data) {
        return systemUserService.decryptPassword(data);
    }

}
