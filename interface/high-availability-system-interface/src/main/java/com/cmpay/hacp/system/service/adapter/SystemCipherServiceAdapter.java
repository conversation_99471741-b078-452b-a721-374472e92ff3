package com.cmpay.hacp.system.service.adapter;

import com.cmpay.hacp.system.service.SystemCipherService;

public abstract class SystemCipherServiceAdapter implements SystemCipherService {
    @Override
    public String getSm2PublicKey(String applicationName) {
        return null;
    }

    @Override
    public String getSm2PrivateKey(String applicationName) {
        return null;
    }

    @Override
    public String getSm4Key(String applicationName) {
        return null;
    }

    @Override
    public String getRsaPublicKey(String applicationName) {
        return null;
    }

    @Override
    public String getRsaPrivateKey(String applicationName) {
        return null;
    }

    @Override
    public String sm2Decrypt(String encryptData) {
        return null;
    }

    @Override
    public String sm4Decrypt(String encryptData) {
        return null;
    }

    @Override
    public String rsaDecrypt(String encryptData) {
        return null;
    }

    @Override
    public String setSm4RandomSalt(String username, String captchaReqId) {
        return null;
    }

    @Override
    public String getSm4RandomSalt(String username, String captchaReqId) {
        return null;
    }

    @Override
    public String otherModuleEncryptAndDecryptData(String key, String data) {
        return null;
    }

    @Override
    public String decryptData(String key, String data) {
        return null;
    }

    @Override
    public String otherModuleEncryptAndDecryptData(String data) {
        return null;
    }

    @Override
    public String otherModuleDecryptData(String data) {
        return null;
    }
}
