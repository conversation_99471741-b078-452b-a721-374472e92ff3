package com.cmpay.hacp.inspection.infrastructure.executor.gateway;

import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.executor.config.PrometheusProperties;
import com.cmpay.hacp.inspection.domain.model.prometheus.*;
import com.cmpay.hacp.inspection.infrastructure.executor.cache.PrometheusTokenCache;
import com.cmpay.lemon.common.exception.BusinessException;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PrometheusGatewayImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class PrometheusGatewayImplTest {

    @Mock
    private PrometheusTokenCache prometheusTokenCache;

    @Mock
    private PrometheusProperties properties;

    @Mock
    private PrometheusProperties.Auth authProperties;

    @Mock
    private PrometheusProperties.Http httpProperties;

    @Mock
    private OkHttpClient httpClient;

    @Mock
    private Call call;

    @Mock
    private Response response;

    @Mock
    private ResponseBody responseBody;

    @InjectMocks
    private PrometheusGatewayImpl prometheusGateway;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();

        // 设置默认的properties mock行为
        when(properties.getAuth()).thenReturn(authProperties);
        when(properties.getHttp()).thenReturn(httpProperties);
        when(authProperties.getUserId()).thenReturn("testUser");
        when(authProperties.getUserKey()).thenReturn("testKey");
        when(authProperties.getTokenUrl()).thenReturn("/api/token");
        when(properties.getBaseUrl()).thenReturn("http://localhost:9090");
        when(properties.getIndicatorUrl()).thenReturn("/api/indicators");

        // 设置HTTP配置默认值
        when(httpProperties.getConnectTimeout()).thenReturn(10000);
        when(httpProperties.getReadTimeout()).thenReturn(30000);
        when(httpProperties.getWriteTimeout()).thenReturn(30000);
        when(httpProperties.getMaxIdleConnections()).thenReturn(10);
        when(httpProperties.getKeepAliveDuration()).thenReturn(5);
        when(httpProperties.isEnableSslCheck()).thenReturn(false);

        // 使用反射设置私有字段
        ReflectionTestUtils.setField(prometheusGateway, "httpClient", httpClient);
        ReflectionTestUtils.setField(prometheusGateway, "objectMapper", objectMapper);
    }

    @Test
    void testGetAuthToken_FromCache_Success() {
        // Given
        PrometheusToken cachedToken = PrometheusToken.builder()
                .accessToken("cached-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
        
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(cachedToken);

        // When
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("cached-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(prometheusTokenCache).getValidToken("testUser");
        verifyNoMoreInteractions(httpClient);
    }

    @Test
    void testGetAuthToken_FetchNew_Success() throws IOException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        
        // Mock HTTP response
        String tokenResponseJson = "{\n" +
                "    \"token\": \"new-access-token\",\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 3600,\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\"\n" +
                "}";
        
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(tokenResponseJson);

        // When
        PrometheusToken result = prometheusGateway.getAuthToken();

        // Then
        assertNotNull(result);
        assertEquals("new-access-token", result.getAccessToken());
        assertEquals("Bearer", result.getTokenType());
        verify(prometheusTokenCache).getValidToken("testUser");
        verify(prometheusTokenCache).cacheToken(eq("testUser"), any(PrometheusToken.class));
    }

    @Test
    void testGetAuthToken_HttpRequestFailed() throws IOException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(500);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("Internal Server Error");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> prometheusGateway.getAuthToken());
        
        assertEquals(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR.getMsgCd(), exception.getMsgCd());
        verify(prometheusTokenCache).getValidToken("testUser");
        verifyNoMoreInteractions(prometheusTokenCache);
    }

    @Test
    void testGetAuthToken_AuthenticationFailed() throws IOException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        String errorResponseJson = "{\n" +
                "    \"token\": null,\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 0,\n" +
                "    \"msgCd\": \"FFM80001\",\n" +
                "    \"msgInfo\": \"Authentication failed\"\n" +
                "}";
        
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(errorResponseJson);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> prometheusGateway.getAuthToken());
        
        assertEquals(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_ConcurrentAccess() throws InterruptedException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser"))
                .thenReturn(null)  // 第一次调用返回null
                .thenReturn(PrometheusToken.builder()  // 后续调用返回缓存的token
                        .accessToken("concurrent-token")
                        .tokenType("Bearer")
                        .expiresAt(LocalDateTime.now().plusHours(1))
                        .build());

        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // When
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    PrometheusToken token = prometheusGateway.getAuthToken();
                    assertNotNull(token);
                } finally {
                    latch.countDown();
                }
            });
        }

        // Then
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        executor.shutdown();
        
        // 验证只有一个线程真正获取了新token，其他线程从缓存获取
        verify(prometheusTokenCache, atLeast(threadCount)).getValidToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_Success() throws IOException {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);

        String responseJson = "{\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\",\n" +
                "    \"data\": [],\n" +
                "    \"total\": 0,\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 100\n" +
                "}";
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(responseJson);

        // When
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result);
        assertEquals("FFM00000", result.getMsgCd());
        assertEquals("Success", result.getMsgInfo());
        assertTrue(result.isSuccess());
        verify(httpClient).newCall(any(Request.class));
    }

    @Test
    void testGetIndicatorDataList_InvalidRequest() {
        // Given
        IndicatorQueryRequest invalidRequest = IndicatorQueryRequest.builder()
                .build(); // 缺少必要字段

        // When
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(invalidRequest);

        // Then
        assertNotNull(result);
        assertEquals("FFM80003", result.getMsgCd());
        assertEquals("请求参数验证失败", result.getMsgInfo());
        assertFalse(result.isSuccess());
        verifyNoInteractions(httpClient);
    }

    @Test
    void testGetIndicatorDataList_TokenExpired() throws IOException {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(401);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("Unauthorized");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> prometheusGateway.getIndicatorDataList(request));
        
        assertEquals(ErrorCodeEnum.PROMETHEUS_TOKEN_EXPIRED.getMsgCd(), exception.getMsgCd());
        verify(prometheusTokenCache).evictToken("testUser");
    }

    @Test
    void testGetIndicatorDataList_QueryError() throws IOException {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(500);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("Internal Server Error");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> prometheusGateway.getIndicatorDataList(request));
        
        assertEquals(ErrorCodeEnum.PROMETHEUS_QUERY_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_UserStatusError() throws IOException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);

        String errorResponseJson = "{\n" +
                "    \"token\": null,\n" +
                "    \"userId\": \"testUser\",\n" +
                "    \"expire\": 0,\n" +
                "    \"msgCd\": \"FFM80002\",\n" +
                "    \"msgInfo\": \"User status error\"\n" +
                "}";

        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(errorResponseJson);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> prometheusGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.PROMETHEUS_AUTH_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_InvalidJsonResponse() throws IOException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("invalid json");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> prometheusGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetAuthToken_IOExceptionDuringRequest() throws IOException {
        // Given
        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(null);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenThrow(new IOException("Network error"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> prometheusGateway.getAuthToken());

        assertEquals(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetIndicatorDataList_WithCompleteRequest() throws IOException {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .zones(Arrays.asList("zone1", "zone2"))
                .specialNameOne("special1")
                .specialNameTwo("special2")
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);

        String responseJson = "{\n" +
                "    \"msgCd\": \"FFM00000\",\n" +
                "    \"msgInfo\": \"Success\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"indicatorName\": \"cpu_usage\",\n" +
                "            \"timestamp\": \"2024-01-01T10:00:00\",\n" +
                "            \"value\": \"75.5\",\n" +
                "            \"zone\": \"zone1\",\n" +
                "            \"one\": \"special1\",\n" +
                "            \"two\": \"special2\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"total\": 1,\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 100\n" +
                "}";

        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(responseJson);

        // When
        IndicatorQueryResponse result = prometheusGateway.getIndicatorDataList(request);

        // Then
        assertNotNull(result);
        assertEquals("FFM00000", result.getMsgCd());
        assertEquals("Success", result.getMsgInfo());
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals(Long.valueOf(1), result.getTotal());
        assertEquals(Integer.valueOf(1), result.getPageNum());
        assertEquals(Integer.valueOf(100), result.getPageSize());
    }

    @Test
    void testGetIndicatorDataList_IOExceptionDuringRequest() throws IOException {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenThrow(new IOException("Network timeout"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> prometheusGateway.getIndicatorDataList(request));

        assertEquals(ErrorCodeEnum.PROMETHEUS_CONNECTION_ERROR.getMsgCd(), exception.getMsgCd());
    }

    @Test
    void testGetIndicatorDataList_InvalidJsonResponse() throws IOException {
        // Given
        IndicatorQueryRequest request = IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();

        PrometheusToken token = PrometheusToken.builder()
                .accessToken("valid-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .build();

        when(prometheusTokenCache.getValidToken("testUser")).thenReturn(token);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("invalid json response");

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
            () -> prometheusGateway.getIndicatorDataList(request));

        assertEquals(ErrorCodeEnum.PROMETHEUS_INVALID_RESPONSE.getMsgCd(), exception.getMsgCd());
    }

    /**
     * 辅助方法：创建有效的PrometheusToken
     */
    private PrometheusToken createValidToken() {
        return PrometheusToken.builder()
                .accessToken("test-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().plusHours(1))
                .createdAt(LocalDateTime.now())
                .build();
    }

    /**
     * 辅助方法：创建过期的PrometheusToken
     */
    private PrometheusToken createExpiredToken() {
        return PrometheusToken.builder()
                .accessToken("expired-token")
                .tokenType("Bearer")
                .expiresAt(LocalDateTime.now().minusHours(1))
                .createdAt(LocalDateTime.now().minusHours(2))
                .build();
    }

    /**
     * 辅助方法：创建基本的IndicatorQueryRequest
     */
    private IndicatorQueryRequest createBasicRequest() {
        return IndicatorQueryRequest.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .indicatorType("host")
                .indicatorName("cpu_usage")
                .pageNum(1)
                .pageSize(100)
                .build();
    }
}
