dependencies {
    api project(':interface:high-availability-base-interface')
    api project(':interface:high-availability-system-interface')
    optional project(':common:high-availability-common')
    optional("com.cmpay:lemon-swagger-starter")
    optional("com.cmpay:lemon-framework-starter-context")
    optional("com.cmpay:cmpay-interface")
    optional('com.cmpay:lemon-framework-mybatis')
    optional("com.cmpay:lemon-framework-starter-security")
}
